// Copyright (c) 2020 Ultimaker B.V.
// CuraEngine is released under the terms of the AGPLv3 or higher.

#ifndef DRAW_ZSEAM_CONFIG
#define DRAW_ZSEAM_CONFIG

#include <cassert>
#include <vector>


namespace cura
{

/*!
 * Class representing a graph matching a flow to a HeightThickness.
 * The graph generally consists of several linear line segments between points at which the temperature and flow are matched.
 */
class draw_zseam_points
{
public:
    struct Point
    {
        const double x_; 
        const double y_; 
        const double z_; 
        Point(const double x, const double y, const double z)
            : x_(x), y_(y) , z_(z)
        {
        }
    };

    std::vector<Point> zSeamPoints_; //!< The points of the graph between which the graph is linearly interpolated

    Point seamPoint = Point(0, 0, 0);

    /*!
     * Get the HeightThickness corresponding to a specific flow.
     *
     * For flows outside of the chart, the HeightThickness at the minimal or maximal flow is returned.
     * When the graph is empty, the @p HeightThickness is returned.
     *
     * \param flow the flow in mm^3/s
     * \param material_print_temperature The default printing temp (backward compatibility for when the graph fails)
     * \return the corresponding temp
     */
    void setClosestPoint(double height);
};

} // namespace curas

#endif // DRAW_ZSEAM_CONFIG
