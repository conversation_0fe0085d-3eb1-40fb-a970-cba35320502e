@echo off
echo 测试构建脚本...

:: 检查当前目录
if not exist "CMakeLists.txt" (
    echo 错误: 不是CuraEngine项目目录
    exit /b 1
)

echo 当前目录正确: %CD%

:: 检查现有的可执行文件
if exist "CuraEngine.exe" (
    echo 找到现有的CuraEngine.exe
    CuraEngine.exe help | findstr "version"
) else (
    echo 未找到CuraEngine.exe
)

:: 检查构建目录
if exist "build\Debug" (
    echo 找到Debug构建目录
    if exist "build\Debug\CuraEngine.exe" (
        echo 找到Debug版本的CuraEngine.exe
    )
)

if exist "build\Release" (
    echo 找到Release构建目录
    if exist "build\Release\CuraEngine.exe" (
        echo 找到Release版本的CuraEngine.exe
    )
)

echo 测试完成
