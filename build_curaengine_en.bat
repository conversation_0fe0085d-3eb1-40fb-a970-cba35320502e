@echo off
setlocal enabledelayedexpansion

:: CuraEngine Auto Build Script
:: Author: AI Assistant
:: Date: 2025-06-21
:: Purpose: Automatically compile CuraEngine and generate executable

echo ========================================
echo CuraEngine Auto Build Script
echo ========================================
echo.

:: Check if current directory is CuraEngine project root
if not exist "CMakeLists.txt" (
    echo Error: Current directory is not CuraEngine project root
    echo Please run this script in CuraEngine project root directory
    pause
    exit /b 1
)

if not exist "src\main.cpp" (
    echo Error: Cannot find source code files, please confirm this is CuraEngine project directory
    pause
    exit /b 1
)

echo Detected CuraEngine project directory: %CD%
echo.

:: Set build type (default Release, can be modified by parameter)
set BUILD_TYPE=Release
if "%1"=="debug" set BUILD_TYPE=Debug
if "%1"=="Debug" set BUILD_TYPE=Debug
if "%1"=="DEBUG" set BUILD_TYPE=Debug

echo Build type: %BUILD_TYPE%
echo.

:: Create build directory
set BUILD_DIR=build\%BUILD_TYPE%
echo Creating build directory: %BUILD_DIR%
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

:: Clean previous build files (optional)
set /p CLEAN_BUILD="Clean previous build files? (y/N): "
if /i "%CLEAN_BUILD%"=="y" (
    echo Cleaning build directory...
    rmdir /s /q "%BUILD_DIR%" 2>nul
    mkdir "%BUILD_DIR%"
    echo Build directory cleaned
)
echo.

:: Step 1: Install Conan dependencies
echo ========================================
echo Step 1/4: Install Conan Dependencies
echo ========================================
echo Installing Conan dependency packages...

:: Check if conan is installed
conan --version >nul 2>&1
if errorlevel 1 (
    echo Error: Conan package manager not found
    echo Please install Conan first: pip install conan
    pause
    exit /b 1
)

:: Install dependencies
echo Executing: conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR%
conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR%
if errorlevel 1 (
    echo.
    echo Warning: Conan dependency installation may have issues, trying to continue build...
    echo If build fails, please check Conan configuration
    echo.
)

echo Conan dependency installation completed
echo.

:: Step 2: Configure CMake
echo ========================================
echo Step 2/4: Configure CMake
echo ========================================
echo Configuring CMake...

:: Check if cmake is installed
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found
    echo Please install CMake and add to PATH environment variable
    pause
    exit /b 1
)

:: Configure CMake
set CMAKE_PRESET=release
if "%BUILD_TYPE%"=="Debug" set CMAKE_PRESET=debug
echo Executing: cmake --preset=%CMAKE_PRESET%
cmake --preset=%CMAKE_PRESET%
if errorlevel 1 (
    echo Error: CMake configuration failed
    echo Please check CMake configuration and dependencies
    pause
    exit /b 1
)

echo CMake configuration completed
echo.

:: Step 3: Compile project
echo ========================================
echo Step 3/4: Compile CuraEngine
echo ========================================
echo Compiling CuraEngine...

:: Compile project
echo Executing: cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine
cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine
if errorlevel 1 (
    echo Error: Compilation failed
    echo Please check compilation error messages
    pause
    exit /b 1
)

echo Compilation completed
echo.

:: Step 4: Copy executable and dependencies
echo ========================================
echo Step 4/4: Copy Files to Project Root
echo ========================================

:: Find executable file
set EXE_PATH=%BUILD_DIR%\CuraEngine.exe
if not exist "%EXE_PATH%" (
    echo Error: Cannot find compiled executable: %EXE_PATH%
    pause
    exit /b 1
)

:: Copy executable file
echo Copying executable file...
copy "%EXE_PATH%" "CuraEngine.exe" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable file
    pause
    exit /b 1
)

:: Copy dependent DLL files
echo Copying dependency library files...
if exist "%BUILD_DIR%\*.dll" (
    copy "%BUILD_DIR%\*.dll" . >nul 2>&1
)

:: Copy ossl-modules directory (if exists)
if exist "%BUILD_DIR%\ossl-modules" (
    if not exist "ossl-modules" mkdir "ossl-modules"
    copy "%BUILD_DIR%\ossl-modules\*" "ossl-modules\" >nul 2>&1
)

echo File copying completed
echo.

:: Verify executable file
echo ========================================
echo Verify Build Results
echo ========================================
echo Testing CuraEngine executable...

CuraEngine.exe help >nul 2>&1
if errorlevel 1 (
    echo Warning: CuraEngine executable may not run properly
    echo Please check if dependency libraries are missing
) else (
    echo Success: CuraEngine executable runs normally
)

:: Display file information
echo.
echo Compiled files:
dir CuraEngine.exe 2>nul
echo.

:: Display version information
echo CuraEngine version information:
CuraEngine.exe help | findstr "version" 2>nul

echo.
echo ========================================
echo Build Completed!
echo ========================================
echo.
echo Executable file location: %CD%\CuraEngine.exe
echo Build directory: %CD%\%BUILD_DIR%
echo.
echo Usage:
echo   CuraEngine.exe help          - Show help information
echo   CuraEngine.exe slice [args]  - Execute slicing operation
echo.

pause
