@echo off
echo ========================================
echo CuraEngine Quick Build Test
echo ========================================

:: Check if current directory is correct
if not exist "CMakeLists.txt" (
    echo Error: Not in CuraEngine project directory
    exit /b 1
)

echo Current directory: %CD%

:: Set build type
set BUILD_TYPE=Debug
if "%1"=="release" set BUILD_TYPE=Release
if "%1"=="Release" set BUILD_TYPE=Release

echo Build type: %BUILD_TYPE%

:: Set build directory
set BUILD_DIR=build\%BUILD_TYPE%
echo Build directory: %BUILD_DIR%

:: Check if build directory exists
if not exist "%BUILD_DIR%" (
    echo Error: Build directory does not exist: %BUILD_DIR%
    echo Please run full build script first
    exit /b 1
)

:: Try to build using existing configuration
echo ========================================
echo Building CuraEngine...
echo ========================================
echo Executing: cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine

cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine

if errorlevel 1 (
    echo Build failed!
    exit /b 1
)

echo Build completed successfully!

:: Copy files
echo ========================================
echo Copying files...
echo ========================================

set EXE_PATH=%BUILD_DIR%\CuraEngine.exe
if not exist "%EXE_PATH%" (
    echo Error: Cannot find executable: %EXE_PATH%
    exit /b 1
)

echo Copying executable...
copy "%EXE_PATH%" "CuraEngine.exe" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable
    exit /b 1
)

echo Copying DLL files...
if exist "%BUILD_DIR%\*.dll" (
    copy "%BUILD_DIR%\*.dll" . >nul 2>&1
)

echo ========================================
echo Testing executable...
echo ========================================

CuraEngine.exe help >nul 2>&1
if errorlevel 1 (
    echo Warning: Executable test failed
) else (
    echo Success: Executable works correctly
)

echo ========================================
echo Quick build completed!
echo ========================================

:: Show file info
echo Executable file:
dir CuraEngine.exe 2>nul

echo.
echo Version info:
CuraEngine.exe help | findstr "version" 2>nul

pause
