# CuraEngine代码迁移更新计划

## 概述
本文档详细记录了将CuraEngine中的所有自定义修改迁移到CuraEngine_NEW中的完整计划。通过git分析发现，共有24个文件被修改，18个新文件被添加，总计1140行新增代码和134行删除代码。

## 修改统计总览
- **修改的文件**: 24个
- **新增的文件**: 18个  
- **代码变更**: +1140/-134行
- **主要功能**: 自适应层高、缝合线配置、擦拭功能、路径优化等

## 第一阶段：头文件修改迁移

### 1.1 include/FffGcodeWriter.h
**修改内容**: 函数签名变更
- 将`findSpiralizedLayerSeamVertexIndex`函数的`const SliceMeshStorage& mesh`参数改为`SliceMeshStorage& mesh`
- **影响**: 移除const限定符，允许函数修改mesh对象

### 1.2 include/LayerPlan.h  
**修改内容**: 新增成员和方法
- 添加成员变量: `Polygons extraLineExtrude_O` (用于额外线段挤出)
- 添加方法: `addWipeTravel(coord_t wall_0_wipe_dist, const Settings& settings)` (擦拭头移动功能)
- **影响**: 扩展层规划功能，支持擦拭和额外挤出

### 1.3 include/PathOrderOptimizer.h
**修改内容**: 方法签名扩展  
- `optimize`方法添加参数: `long seamx = -1, long seamy = -1`
- **影响**: 支持自定义缝合位置坐标

### 1.4 include/pathPlanning/GCodePath.h
**修改内容**: 新增擦拭相关成员变量
- 添加`bool wipeRetract{ false }`: 擦拭回抽标志
- 添加`double wipeLength{ 0.2 }`: 擦拭长度参数
- **影响**: 支持擦拭功能的路径规划

### 1.5 include/settings/AdaptiveLayerHeights.h
**修改内容**: 扩展自适应层高功能
- 添加`#include "HeightThicknessGraph.h"`头文件引用
- 在AdaptiveLayer类中添加`int speedRatio_`成员变量（速度比例）
- 添加新构造函数：`AdaptiveLayerHeights(const coord_t base_layer_height, const HeightThicknessGraph height_thickness, const MeshGroup* meshgroup)`
- 添加新方法：`getCurrThickness`用于获取当前厚度
- **影响**: 支持基于高度-厚度图的自适应层高和速度控制

### 1.6 include/sliceDataStorage.h
**修改内容**: 扩展层数据存储
- 在SliceLayer类中添加`int speedRatioByUser = 100`（用户设定速度比例）
- 添加`uint32_t MainPartNum`（主要部件数量，中文注释）
- **影响**: 支持用户自定义速度和部件统计

### 1.7 include/utils/polygon.h
**修改内容**: 增强多边形安全性
- 在`operator[]`方法中添加边界检查
- 添加调试代码用于检测越界访问
- **影响**: 提高多边形操作的安全性和调试能力

### 1.8 include/utils/polygonUtils.h  
**修改内容**: 修改1行代码
- **功能**: 函数签名或声明的调整

## 第二阶段：源文件修改迁移

### 2.1 src/FffGcodeWriter.cpp (最大修改: +248/-35行)
**修改内容**: G代码生成核心逻辑
- 实现`findSpiralizedLayerSeamVertexIndex`函数修改
- 可能包括新的G代码生成逻辑、缝合处理优化
- **重要性**: 高 - 影响G代码输出质量

### 2.2 src/LayerPlan.cpp (第二大修改: +401/-70行)  
**修改内容**: 层规划算法大幅优化
- 实现`addWipeTravel`方法
- 实现`extraLineExtrude_O`功能
- 层规划算法的大幅优化和新功能
- **重要性**: 高 - 核心打印逻辑

### 2.3 src/settings/Settings.cpp (大增加: +119行)
**修改内容**: 设置系统扩展
- 大量新的设置功能和配置选项
- **重要性**: 高 - 影响所有配置功能

### 2.4 src/settings/AdaptiveLayerHeights.cpp (大增加: +92行)
**修改内容**: 全新功能实现
- 全新的自适应层高功能实现
- **重要性**: 高 - 新核心功能

### 2.5 src/FffPolygonGenerator.cpp (+83/-7行)
**修改内容**: 多边形生成优化
- 多边形生成算法的优化
- 新的处理逻辑或功能增强

### 2.6 src/utils/polygonUtils.cpp (+53行)
**修改内容**: 工具函数扩展
- 新的多边形工具函数实现

### 2.7 src/InsetOrderOptimizer.cpp (+46/-3行)
**修改内容**: 内缩优化算法
- 内缩顺序优化算法的改进
- 新功能添加

### 2.8 src/LayerPlanBuffer.cpp (+32/-4行)
**修改内容**: 缓冲区管理
- 层缓冲区管理相关的修改

### 2.9 其他源文件修改 (小幅修改)
- src/layerPart.cpp (+9行): 层部件处理新功能
- src/raft.cpp (+8行): 支撑底板功能增强  
- src/WallsComputation.cpp (+8/-3行): 墙体计算优化
- src/WallToolPaths.cpp (+4/-3行): 墙体路径调整
- src/communication/ArcusCommunication.cpp (+2/-2行): 通信协议调整
- src/progress/Progress.cpp (+2/-2行): 进度报告调整
- src/SkirtBrim.cpp (+1/-1行): 边缘处理调整
- src/sliceDataStorage.cpp (+1行): 数据存储调整

## 第三阶段：新增文件迁移

### 3.1 新增设置相关文件
- `include/settings/DrawZSeamConfig.h`: 缝合线绘制配置头文件
  - 定义`draw_zseam_points`类，包含3D点结构和缝合点管理
  - 提供`setClosestPoint`方法用于设置最近的缝合点
- `src/settings/DrawZSeamConfig.cpp`: 缝合线绘制配置实现
  - 实现缝合线绘制的具体算法和逻辑
- `include/settings/HeightThicknessGraph.h`: 高度-厚度图形头文件
  - 定义`HeightThicknessGraph`类，用于管理高度与厚度的映射关系
  - 包含`Datum`结构存储高度-厚度数据点
  - 提供`getThickness`方法进行线性插值计算
- `src/settings/HeightThicknessGraph.cpp`: 高度-厚度图形实现
  - 实现高度-厚度图形的具体算法
- `include/settings/types/HeightThickness.h`: 高度-厚度类型定义
  - 定义`HeightThickness`结构体，作为double的封装
  - 提供算术运算符重载，支持加减运算

### 3.2 新增构建脚本
- `build_curaengine.bat`: 主构建脚本（205行）
  - 完整的自动化编译流程：检查环境→安装Conan依赖→配置CMake→编译→复制文件
  - 支持Debug/Release构建类型选择
  - 包含错误检查和用户交互
  - 自动复制可执行文件和依赖库到项目根目录
- `build_curaengine_en.bat`: 英文版构建脚本
- `build_curaengine_final.bat`: 最终版构建脚本
- `build_curaengine_fixed.bat`: 修复版构建脚本
- `quick_build.bat`: 快速构建脚本
- `quick_build_test.bat`: 快速构建测试脚本
- `test_build.bat`: 测试构建脚本

### 3.3 新增文档和配置
- `BUILD_SCRIPTS_README.md`: 构建脚本说明文档
- `使用说明.txt`: 中文使用说明
- `脚本验证报告.md`: 脚本验证报告
- `launch.vs.json`: Visual Studio启动配置

### 3.4 新增依赖库
- `Arcus.dll`: Arcus通信库
- `polyclipping.dll`: 多边形裁剪库
- `ossl-modules/`: OpenSSL模块目录
  - `fips.dll`: FIPS模块
  - `legacy.dll`: 传统模块

## 第四阶段：验证和测试

### 4.1 编译验证
- 在CuraEngine_NEW中验证所有修改后的代码能够正常编译
- 解决可能的依赖问题和编译错误

### 4.2 功能测试  
- 测试新增的自适应层高功能
- 测试缝合线配置功能
- 测试擦拭功能
- 验证路径优化效果

### 4.3 回归测试
- 确保原有功能不受影响
- 验证G代码输出质量
- 性能测试

## 执行顺序建议

1. **先迁移头文件** - 建立接口基础
2. **再迁移核心源文件** - 按重要性排序
3. **然后迁移新增文件** - 补充完整功能
4. **最后验证测试** - 确保质量

## 风险评估

- **高风险**: src/FffGcodeWriter.cpp, src/LayerPlan.cpp (核心逻辑大幅修改)
- **中风险**: src/settings/Settings.cpp, src/settings/AdaptiveLayerHeights.cpp (新功能)
- **低风险**: 构建脚本、文档文件 (不影响核心功能)

## 详细执行检查清单

### 阶段1检查清单 - 头文件迁移
- [ ] include/FffGcodeWriter.h - 函数签名修改
- [ ] include/LayerPlan.h - 新增成员变量和方法
- [ ] include/PathOrderOptimizer.h - optimize方法参数扩展
- [ ] include/pathPlanning/GCodePath.h - 擦拭相关成员
- [ ] include/settings/AdaptiveLayerHeights.h - 自适应层高扩展
- [ ] include/sliceDataStorage.h - 层数据存储扩展
- [ ] include/utils/polygon.h - 多边形安全性增强
- [ ] include/utils/polygonUtils.h - 函数声明调整

### 阶段2检查清单 - 源文件迁移（按优先级）
**高优先级（核心功能）:**
- [ ] src/FffGcodeWriter.cpp (+248/-35行) - G代码生成核心
- [ ] src/LayerPlan.cpp (+401/-70行) - 层规划算法核心
- [ ] src/settings/Settings.cpp (+119行) - 设置系统扩展
- [ ] src/settings/AdaptiveLayerHeights.cpp (+92行) - 自适应层高实现

**中优先级（算法优化）:**
- [ ] src/FffPolygonGenerator.cpp (+83/-7行) - 多边形生成
- [ ] src/utils/polygonUtils.cpp (+53行) - 工具函数
- [ ] src/InsetOrderOptimizer.cpp (+46/-3行) - 内缩优化
- [ ] src/LayerPlanBuffer.cpp (+32/-4行) - 缓冲区管理

**低优先级（小幅调整）:**
- [ ] src/layerPart.cpp (+9行) - 层部件处理
- [ ] src/raft.cpp (+8行) - 支撑底板
- [ ] src/WallsComputation.cpp (+8/-3行) - 墙体计算
- [ ] src/WallToolPaths.cpp (+4/-3行) - 墙体路径
- [ ] src/communication/ArcusCommunication.cpp (+2/-2行) - 通信
- [ ] src/progress/Progress.cpp (+2/-2行) - 进度报告
- [ ] src/SkirtBrim.cpp (+1/-1行) - 边缘处理
- [ ] src/sliceDataStorage.cpp (+1行) - 数据存储

### 阶段3检查清单 - 新增文件迁移
**设置相关文件:**
- [ ] include/settings/DrawZSeamConfig.h - 缝合线配置头文件
- [ ] src/settings/DrawZSeamConfig.cpp - 缝合线配置实现
- [ ] include/settings/HeightThicknessGraph.h - 高度厚度图头文件
- [ ] src/settings/HeightThicknessGraph.cpp - 高度厚度图实现
- [ ] include/settings/types/HeightThickness.h - 类型定义

**构建脚本:**
- [ ] build_curaengine.bat - 主构建脚本
- [ ] build_curaengine_en.bat - 英文版构建脚本
- [ ] build_curaengine_final.bat - 最终版构建脚本
- [ ] build_curaengine_fixed.bat - 修复版构建脚本
- [ ] quick_build.bat - 快速构建脚本
- [ ] quick_build_test.bat - 快速构建测试脚本
- [ ] test_build.bat - 测试构建脚本

**文档和配置:**
- [ ] BUILD_SCRIPTS_README.md - 构建说明文档
- [ ] 使用说明.txt - 中文使用说明
- [ ] 脚本验证报告.md - 验证报告
- [ ] launch.vs.json - VS启动配置

**依赖库:**
- [ ] Arcus.dll - Arcus通信库
- [ ] polyclipping.dll - 多边形裁剪库
- [ ] ossl-modules/ - OpenSSL模块目录

### 阶段4检查清单 - 验证测试
- [ ] 编译验证 - 确保所有代码正常编译
- [ ] 功能测试 - 测试新增功能
- [ ] 回归测试 - 确保原有功能正常
- [ ] 性能测试 - 验证性能影响

## 注意事项

1. 每个文件迁移后都要进行编译测试
2. 保持与原始代码的一致性
3. 注意中文注释的编码问题
4. 确保所有依赖库正确配置
5. 记录每次修改的详细内容和测试结果
6. 按照优先级顺序进行迁移，先核心后边缘
7. 每个阶段完成后进行一次完整编译测试
