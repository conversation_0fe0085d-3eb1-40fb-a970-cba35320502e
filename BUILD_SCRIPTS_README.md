# CuraEngine 自动构建脚本

本目录包含了用于自动编译CuraEngine的脚本，让您可以轻松获得最新的可执行文件。

## 脚本文件说明

### 1. `build_curaengine.bat` - 完整构建脚本 (推荐)
这是主要的构建脚本，包含完整的构建流程：
- 自动检查和安装Conan依赖
- 配置CMake
- 编译CuraEngine
- 复制可执行文件和依赖库到项目根目录

**使用方法：**
```cmd
# 在Windows命令提示符或PowerShell中运行
# Release版本构建 (推荐)
build_curaengine.bat

# Debug版本构建
build_curaengine.bat debug
```

### 2. `quick_build.bat` - 快速构建脚本
用于已经配置好依赖的快速重新编译：

**使用方法：**
```cmd
# 快速Release构建
quick_build.bat

# 快速Debug构建
quick_build.bat debug
```

### 3. 手动构建命令
如果脚本无法运行，您也可以手动执行以下命令：

```cmd
# 1. 安装Conan依赖
conan install . --build=missing -s build_type=Release --output-folder=build\Release

# 2. 配置CMake
cmake --preset=conan-release

# 3. 编译
cmake --build build\Release --config Release --target CuraEngine

# 4. 复制文件
copy build\Release\CuraEngine.exe .
copy build\Release\*.dll .
```

## 系统要求

在运行构建脚本之前，请确保您的系统已安装以下工具：

### 必需工具
1. **Visual Studio 2019/2022** 或 **Visual Studio Build Tools**
   - 包含MSVC编译器和Windows SDK
   
2. **CMake** (版本 3.20 或更高)
   - 下载地址: https://cmake.org/download/
   - 确保添加到PATH环境变量

3. **Conan** (版本 1.58.0 或更高)
   ```bash
   pip install conan
   ```

4. **Python** (用于Conan)
   - Python 3.7 或更高版本

### 可选工具
- **Git** (用于获取最新源代码)
- **Ninja** (更快的构建系统，Conan会自动安装)

## 首次使用步骤

1. **克隆或更新CuraEngine源代码**
   ```bash
   git clone https://github.com/Ultimaker/CuraEngine.git
   cd CuraEngine
   
   # 或者更新现有代码
   git pull origin main
   ```

2. **运行完整构建脚本**
   ```bash
   build_curaengine.bat
   ```

3. **等待构建完成**
   - 首次构建可能需要较长时间（下载依赖）
   - 后续构建会更快

4. **验证结果**
   ```bash
   CuraEngine.exe help
   ```

## 构建输出

成功构建后，您将在项目根目录得到：
- `CuraEngine.exe` - 主要可执行文件
- `*.dll` - 依赖库文件
- `ossl-modules/` - OpenSSL模块（如果需要）

## 故障排除

### 常见问题

1. **Conan配置错误**
   ```bash
   # 重置Conan配置
   conan config install https://github.com/ultimaker/conan-config.git
   ```

2. **编译器找不到**
   - 确保安装了Visual Studio或Build Tools
   - 运行脚本前先执行：
     ```bash
     "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
     ```

3. **CMake配置失败**
   - 检查CMake版本：`cmake --version`
   - 确保CMake在PATH中

4. **依赖库缺失**
   - 重新运行完整构建脚本
   - 检查是否所有DLL文件都已复制

### 清理构建

如果遇到问题，可以清理构建文件：
```bash
# 删除构建目录
rmdir /s build

# 或使用PowerShell脚本的清理选项
.\build_curaengine.ps1 -Clean
```

## 高级用法

### 自定义构建选项

您可以修改脚本中的CMake选项来启用/禁用特定功能：
- `ENABLE_ARCUS` - Arcus通信支持
- `ENABLE_PLUGINS` - 插件支持
- `ENABLE_TESTING` - 单元测试
- `ENABLE_BENCHMARKS` - 性能测试

### 持续集成

这些脚本也可以用于CI/CD流水线：
```bash
# 无交互模式构建
echo n | build_curaengine.bat
```

## 获取帮助

如果您遇到问题：
1. 检查构建日志中的错误信息
2. 确认所有依赖工具已正确安装
3. 尝试清理后重新构建
4. 查看CuraEngine官方文档

## 更新脚本

要获取脚本的最新版本，请重新运行AI助手或从项目仓库获取更新。

---

**注意**: 这些脚本会自动处理大部分构建复杂性，但首次运行可能需要较长时间来下载和编译依赖项。请耐心等待构建完成。
