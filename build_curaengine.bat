@echo off
setlocal enabledelayedexpansion

:: CuraEngine 自动编译脚本
:: 作者: AI Assistant
:: 日期: 2025-06-21
:: 用途: 自动编译CuraEngine并生成可执行文件

echo ========================================
echo CuraEngine 自动编译脚本
echo ========================================
echo.

:: 检查当前目录是否为CuraEngine项目根目录
if not exist "CMakeLists.txt" (
    echo 错误: 当前目录不是CuraEngine项目根目录
    echo 请在CuraEngine项目根目录下运行此脚本
    pause
    exit /b 1
)

if not exist "src\main.cpp" (
    echo 错误: 找不到源代码文件，请确认这是CuraEngine项目目录
    pause
    exit /b 1
)

echo 检测到CuraEngine项目目录: %CD%
echo.

:: 设置构建类型 (默认为Release，可以通过参数修改)
set BUILD_TYPE=Release
if "%1"=="debug" set BUILD_TYPE=Debug
if "%1"=="Debug" set BUILD_TYPE=Debug
if "%1"=="DEBUG" set BUILD_TYPE=Debug

echo 构建类型: %BUILD_TYPE%
echo.

:: 创建构建目录
set BUILD_DIR=build\%BUILD_TYPE%
echo 创建构建目录: %BUILD_DIR%
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

:: 清理之前的构建文件（可选）
set /p CLEAN_BUILD="是否清理之前的构建文件? (y/N): "
if /i "%CLEAN_BUILD%"=="y" (
    echo 清理构建目录...
    rmdir /s /q "%BUILD_DIR%" 2>nul
    mkdir "%BUILD_DIR%"
    echo 构建目录已清理
)
echo.

:: 步骤1: 安装Conan依赖
echo ========================================
echo 步骤 1/4: 安装Conan依赖
echo ========================================
echo 正在安装Conan依赖包...

:: 检查conan是否安装
conan --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Conan包管理器
    echo 请先安装Conan: pip install conan
    pause
    exit /b 1
)

:: 安装依赖
echo 执行: conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR%
conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR%
if errorlevel 1 (
    echo.
    echo 警告: Conan依赖安装可能有问题，尝试继续构建...
    echo 如果构建失败，请检查Conan配置
    echo.
)

echo Conan依赖安装完成
echo.

:: 步骤2: 配置CMake
echo ========================================
echo 步骤 2/4: 配置CMake
echo ========================================
echo 正在配置CMake...

:: 检查cmake是否安装
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake
    echo 请先安装CMake并添加到PATH环境变量
    pause
    exit /b 1
)

:: 配置CMake
echo 执行: cmake --preset=conan-%BUILD_TYPE%
cmake --preset=conan-%BUILD_TYPE%
if errorlevel 1 (
    echo 错误: CMake配置失败
    echo 请检查CMake配置和依赖项
    pause
    exit /b 1
)

echo CMake配置完成
echo.

:: 步骤3: 编译项目
echo ========================================
echo 步骤 3/4: 编译CuraEngine
echo ========================================
echo 正在编译CuraEngine...

:: 编译项目
echo 执行: cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine
cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine
if errorlevel 1 (
    echo 错误: 编译失败
    echo 请检查编译错误信息
    pause
    exit /b 1
)

echo 编译完成
echo.

:: 步骤4: 复制可执行文件和依赖
echo ========================================
echo 步骤 4/4: 复制文件到项目根目录
echo ========================================

:: 查找可执行文件
set EXE_PATH=%BUILD_DIR%\CuraEngine.exe
if not exist "%EXE_PATH%" (
    echo 错误: 找不到编译后的可执行文件: %EXE_PATH%
    pause
    exit /b 1
)

:: 复制可执行文件
echo 复制可执行文件...
copy "%EXE_PATH%" "CuraEngine.exe" >nul
if errorlevel 1 (
    echo 错误: 复制可执行文件失败
    pause
    exit /b 1
)

:: 复制依赖的DLL文件
echo 复制依赖库文件...
if exist "%BUILD_DIR%\*.dll" (
    copy "%BUILD_DIR%\*.dll" . >nul 2>&1
)

:: 复制ossl-modules目录（如果存在）
if exist "%BUILD_DIR%\ossl-modules" (
    if not exist "ossl-modules" mkdir "ossl-modules"
    copy "%BUILD_DIR%\ossl-modules\*" "ossl-modules\" >nul 2>&1
)

echo 文件复制完成
echo.

:: 验证可执行文件
echo ========================================
echo 验证编译结果
echo ========================================
echo 测试CuraEngine可执行文件...

CuraEngine.exe help >nul 2>&1
if errorlevel 1 (
    echo 警告: CuraEngine可执行文件可能无法正常运行
    echo 请检查是否缺少依赖库
) else (
    echo ✓ CuraEngine可执行文件运行正常
)

:: 显示文件信息
echo.
echo 编译完成的文件:
dir CuraEngine.exe 2>nul
echo.

:: 显示版本信息
echo CuraEngine版本信息:
CuraEngine.exe help | findstr "version" 2>nul

echo.
echo ========================================
echo 编译完成!
echo ========================================
echo.
echo 可执行文件位置: %CD%\CuraEngine.exe
echo 构建目录: %CD%\%BUILD_DIR%
echo.
echo 使用方法:
echo   CuraEngine.exe help          - 显示帮助信息
echo   CuraEngine.exe slice [参数]  - 执行切片操作
echo.

pause
