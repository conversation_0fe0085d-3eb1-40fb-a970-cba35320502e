@echo off
setlocal enabledelayedexpansion

:: CuraEngine Final Build Script
:: This script works around configuration issues by using existing builds

echo ========================================
echo CuraEngine Final Build Script
echo ========================================
echo.

:: Check if current directory is CuraEngine project root
if not exist "CMakeLists.txt" (
    echo Error: Current directory is not CuraEngine project root
    pause
    exit /b 1
)

echo Detected CuraEngine project directory: %CD%
echo.

:: Set build type
set BUILD_TYPE=Debug
if "%1"=="release" set BUILD_TYPE=Release
if "%1"=="Release" set BUILD_TYPE=Release

echo Build type: %BUILD_TYPE%
echo.

:: Set build directory
set BUILD_DIR=build\%BUILD_TYPE%
echo Build directory: %BUILD_DIR%

:: Check if build directory exists and has configuration
if not exist "%BUILD_DIR%" (
    echo Error: Build directory does not exist: %BUILD_DIR%
    echo.
    echo This script requires an existing build configuration.
    echo Please ensure you have:
    echo 1. Visual Studio 2022 installed
    echo 2. Conan package manager installed
    echo 3. Run the initial setup manually
    echo.
    echo Manual setup commands:
    echo   mkdir %BUILD_DIR%
    echo   conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR%
    echo   cmake --preset=%BUILD_TYPE%
    echo.
    pause
    exit /b 1
)

if not exist "%BUILD_DIR%\build.ninja" (
    echo Warning: Build configuration may be incomplete
    echo Attempting to reconfigure...
    
    :: Try to configure with existing dependencies
    set CMAKE_PRESET=debug
    if "%BUILD_TYPE%"=="Release" set CMAKE_PRESET=release
    
    echo Executing: cmake --preset=!CMAKE_PRESET!
    cmake --preset=!CMAKE_PRESET!
    
    if errorlevel 1 (
        echo Error: Configuration failed
        echo Please check your development environment setup
        pause
        exit /b 1
    )
)

:: Build the project
echo ========================================
echo Building CuraEngine...
echo ========================================
echo Executing: cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine

cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine

if errorlevel 1 (
    echo Error: Build failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo Build completed successfully!
echo.

:: Copy files to project root
echo ========================================
echo Copying files to project root...
echo ========================================

set EXE_PATH=%BUILD_DIR%\CuraEngine.exe
if not exist "%EXE_PATH%" (
    echo Error: Cannot find executable: %EXE_PATH%
    pause
    exit /b 1
)

echo Copying executable...
copy "%EXE_PATH%" "CuraEngine.exe" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable
    pause
    exit /b 1
)

echo Copying DLL files...
if exist "%BUILD_DIR%\*.dll" (
    copy "%BUILD_DIR%\*.dll" . >nul 2>&1
    echo DLL files copied
)

if exist "%BUILD_DIR%\ossl-modules" (
    if not exist "ossl-modules" mkdir "ossl-modules"
    copy "%BUILD_DIR%\ossl-modules\*" "ossl-modules\" >nul 2>&1
    echo OpenSSL modules copied
)

echo File copying completed
echo.

:: Test the executable
echo ========================================
echo Testing executable...
echo ========================================

CuraEngine.exe help >nul 2>&1
if errorlevel 1 (
    echo Warning: Executable test failed
    echo The executable may be missing dependencies
) else (
    echo Success: Executable works correctly
)

:: Show results
echo ========================================
echo Build Results
echo ========================================

echo Executable file:
dir CuraEngine.exe 2>nul

echo.
echo Version information:
CuraEngine.exe help | findstr "version" 2>nul

echo.
echo ========================================
echo Build Completed Successfully!
echo ========================================
echo.
echo Executable location: %CD%\CuraEngine.exe
echo Build directory: %CD%\%BUILD_DIR%
echo.
echo Usage:
echo   CuraEngine.exe help          - Show help information
echo   CuraEngine.exe slice [args]  - Execute slicing operation
echo.
echo For future builds, you can use this script again or:
echo   cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine
echo   copy %BUILD_DIR%\CuraEngine.exe .
echo   copy %BUILD_DIR%\*.dll .
echo.

pause
