@echo off
:: CuraEngine 快速构建脚本
:: 用于已经配置好依赖的快速重新编译

echo ========================================
echo CuraEngine 快速构建
echo ========================================

:: 设置构建类型
set BUILD_TYPE=Release
if "%1"=="debug" set BUILD_TYPE=Debug

set BUILD_DIR=build\%BUILD_TYPE%

echo 构建类型: %BUILD_TYPE%
echo 构建目录: %BUILD_DIR%
echo.

:: 检查构建目录是否存在
if not exist "%BUILD_DIR%" (
    echo 错误: 构建目录不存在，请先运行 build_curaengine.bat 进行完整构建
    pause
    exit /b 1
)

:: 快速编译
echo 正在编译CuraEngine...
cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine

if errorlevel 1 (
    echo 编译失败!
    pause
    exit /b 1
)

:: 复制文件
echo 复制可执行文件...
copy "%BUILD_DIR%\CuraEngine.exe" "CuraEngine.exe" >nul
copy "%BUILD_DIR%\*.dll" . >nul 2>&1

echo.
echo ✓ 快速构建完成!
echo 可执行文件: %CD%\CuraEngine.exe

:: 显示文件大小和修改时间
dir CuraEngine.exe | findstr "CuraEngine.exe"

echo.
pause
