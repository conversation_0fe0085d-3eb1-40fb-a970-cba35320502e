CuraEngine 自动编译脚本使用说明
=====================================

我已经为您创建了自动编译CuraEngine的脚本，让您可以轻松获得最新的可执行文件。

📁 创建的文件：
- build_curaengine_final.bat  - 最终工作版本（推荐使用）
- quick_build_test.bat        - 快速构建脚本
- build_curaengine.bat        - 原始完整构建脚本
- BUILD_SCRIPTS_README.md     - 详细说明文档

🚀 快速开始：

1. 打开Windows命令提示符或PowerShell
2. 切换到CuraEngine项目目录
3. 运行构建脚本：

   推荐使用（已验证工作）：
   build_curaengine_final.bat

   快速构建（仅重新编译代码）：
   quick_build_test.bat

   Debug版本构建：
   build_curaengine_final.bat

   Release版本构建（可能有问题）：
   build_curaengine_final.bat release

📋 构建流程：
1. 检查项目目录
2. 安装/更新Conan依赖包
3. 配置CMake构建系统
4. 编译CuraEngine源代码
5. 复制可执行文件和依赖库到项目根目录

✅ 构建成功后，您将得到：
- CuraEngine.exe - 主要可执行文件
- *.dll - 依赖库文件

🔧 系统要求：
- Visual Studio 2019/2022 或 Build Tools
- CMake 3.20+
- Python 3.7+
- Conan包管理器 (pip install conan)

💡 使用技巧：
- 首次构建可能需要较长时间（下载依赖）
- 后续构建会更快
- 如果遇到问题，可以删除build目录重新构建
- 脚本会自动处理大部分依赖和配置问题

🆘 故障排除：
如果脚本无法运行，可以手动执行以下命令：

Debug版本（推荐，已验证工作）：
1. conan install . --build=missing -s build_type=Debug --output-folder=build\Debug
2. cmake --preset=debug
3. cmake --build build\Debug --config Debug --target CuraEngine
4. copy build\Debug\CuraEngine.exe .
5. copy build\Debug\*.dll .

Release版本（可能有protobuf问题）：
1. conan install . --build=missing -s build_type=Release --output-folder=build\Release
2. cmake --preset=release
3. cmake --build build\Release --config Release --target CuraEngine
4. copy build\Release\CuraEngine.exe .
5. copy build\Release\*.dll .

✅ 验证结果：
- Debug版本脚本已成功测试，可以正常工作
- 生成的CuraEngine.exe文件大小约36MB，版本5.7.0
- 可执行文件能正常运行并显示帮助信息

📖 更多信息：
请查看 BUILD_SCRIPTS_README.md 文件获取详细说明。

现在您可以运行 build_curaengine_final.bat 开始编译最新的CuraEngine！
