@echo off
setlocal enabledelayedexpansion

:: CuraEngine Auto Build Script (Fixed Version)
:: Author: AI Assistant
:: Date: 2025-06-21

echo ========================================
echo CuraEngine Auto Build Script (Fixed)
echo ========================================
echo.

:: Check if current directory is CuraEngine project root
if not exist "CMakeLists.txt" (
    echo Error: Current directory is not CuraEngine project root
    echo Please run this script in CuraEngine project root directory
    pause
    exit /b 1
)

if not exist "src\main.cpp" (
    echo Error: Cannot find source code files
    pause
    exit /b 1
)

echo Detected CuraEngine project directory: %CD%
echo.

:: Set build type
set BUILD_TYPE=Release
if "%1"=="debug" set BUILD_TYPE=Debug
if "%1"=="Debug" set BUILD_TYPE=Debug
if "%1"=="DEBUG" set BUILD_TYPE=Debug

echo Build type: %BUILD_TYPE%
echo.

:: Create build directory
set BUILD_DIR=build\%BUILD_TYPE%
echo Creating build directory: %BUILD_DIR%
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

:: Clean previous build files (optional)
set /p CLEAN_BUILD="Clean previous build files? (y/N): "
if /i "%CLEAN_BUILD%"=="y" (
    echo Cleaning build directory...
    rmdir /s /q "%BUILD_DIR%" 2>nul
    mkdir "%BUILD_DIR%"
    echo Build directory cleaned
)
echo.

:: Step 1: Install Conan dependencies (with error handling)
echo ========================================
echo Step 1/4: Install Conan Dependencies
echo ========================================

:: Check if conan is installed
conan --version >nul 2>&1
if errorlevel 1 (
    echo Error: Conan package manager not found
    echo Please install Conan first: pip install conan
    pause
    exit /b 1
)

echo Installing Conan dependency packages...
echo Executing: conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR%

:: Try Conan install with error handling
conan install . --build=missing -s build_type=%BUILD_TYPE% --output-folder=%BUILD_DIR% 2>conan_error.log
set CONAN_RESULT=%errorlevel%

if %CONAN_RESULT% neq 0 (
    echo.
    echo Warning: Conan dependency installation failed
    echo Error details:
    type conan_error.log 2>nul
    echo.
    echo Trying to continue with existing dependencies...
    echo If build fails, please check Conan configuration
    echo.
) else (
    echo Conan dependency installation completed successfully
    del conan_error.log 2>nul
)
echo.

:: Step 2: Configure CMake
echo ========================================
echo Step 2/4: Configure CMake
echo ========================================

:: Check if cmake is installed
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found
    echo Please install CMake and add to PATH environment variable
    pause
    exit /b 1
)

echo Configuring CMake...

:: Set correct preset name based on build type
set CMAKE_PRESET=release
if "%BUILD_TYPE%"=="Debug" set CMAKE_PRESET=debug

echo Executing: cmake --preset=%CMAKE_PRESET%

:: Try CMake configuration
cmake --preset=%CMAKE_PRESET% 2>cmake_error.log
set CMAKE_RESULT=%errorlevel%

if %CMAKE_RESULT% neq 0 (
    echo Error: CMake configuration failed
    echo Error details:
    type cmake_error.log 2>nul
    echo.
    echo Please check:
    echo 1. Visual Studio is properly installed
    echo 2. Conan dependencies are correctly installed
    echo 3. Environment variables are set correctly
    pause
    exit /b 1
) else (
    echo CMake configuration completed successfully
    del cmake_error.log 2>nul
)
echo.

:: Step 3: Compile project
echo ========================================
echo Step 3/4: Compile CuraEngine
echo ========================================

echo Compiling CuraEngine...
echo Executing: cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine

:: Compile project
cmake --build %BUILD_DIR% --config %BUILD_TYPE% --target CuraEngine 2>build_error.log
set BUILD_RESULT=%errorlevel%

if %BUILD_RESULT% neq 0 (
    echo Error: Compilation failed
    echo Error details:
    type build_error.log 2>nul
    echo.
    echo Please check the compilation errors above
    pause
    exit /b 1
) else (
    echo Compilation completed successfully
    del build_error.log 2>nul
)
echo.

:: Step 4: Copy executable and dependencies
echo ========================================
echo Step 4/4: Copy Files to Project Root
echo ========================================

:: Find executable file
set EXE_PATH=%BUILD_DIR%\CuraEngine.exe
if not exist "%EXE_PATH%" (
    echo Error: Cannot find compiled executable: %EXE_PATH%
    echo Build may have failed silently
    pause
    exit /b 1
)

:: Copy executable file
echo Copying executable file...
copy "%EXE_PATH%" "CuraEngine.exe" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable file
    pause
    exit /b 1
)

:: Copy dependent DLL files
echo Copying dependency library files...
if exist "%BUILD_DIR%\*.dll" (
    copy "%BUILD_DIR%\*.dll" . >nul 2>&1
    echo DLL files copied
) else (
    echo No DLL files found to copy
)

:: Copy ossl-modules directory (if exists)
if exist "%BUILD_DIR%\ossl-modules" (
    if not exist "ossl-modules" mkdir "ossl-modules"
    copy "%BUILD_DIR%\ossl-modules\*" "ossl-modules\" >nul 2>&1
    echo OpenSSL modules copied
)

echo File copying completed
echo.

:: Verify executable file
echo ========================================
echo Verify Build Results
echo ========================================
echo Testing CuraEngine executable...

CuraEngine.exe help >nul 2>&1
if errorlevel 1 (
    echo Warning: CuraEngine executable may not run properly
    echo This could indicate missing dependency libraries
    echo Try running: CuraEngine.exe help
) else (
    echo Success: CuraEngine executable runs normally
)

:: Display file information
echo.
echo Build Results:
echo ========================================
dir CuraEngine.exe 2>nul
echo.

:: Display version information
echo Version Information:
CuraEngine.exe help | findstr "version" 2>nul

echo.
echo ========================================
echo Build Completed Successfully!
echo ========================================
echo.
echo Executable file location: %CD%\CuraEngine.exe
echo Build directory: %CD%\%BUILD_DIR%
echo.
echo Usage:
echo   CuraEngine.exe help          - Show help information
echo   CuraEngine.exe slice [args]  - Execute slicing operation
echo.
echo Next time you can use quick_build_test.bat for faster rebuilds
echo.

pause
