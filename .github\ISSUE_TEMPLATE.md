<!--
The following template is useful for filing new issues. Processing an issue will go much faster when this is filled out, and issues which do not use this template will be removed.

Before filing, PLEASE check if the issue already exists (either open or closed) by using the search bar on the issues page. If it does, comment there. Even if it's closed, we can reopen it based on your comment.

Also, please note the application version in the title of the issue. For example: "[3.2.1] Cannot connect to 3rd-party printer". Please do not write things like "Request:" or "[BUG]" in the title; this is what labels are for.

It is also helpful to attach a project (.3mf or .curaproject) file and Cura log file so we can debug issues quicker.
Information about how to find the log file can be found at https://github.com/Ultimaker/Cura/wiki/Cura-Preferences-and-Settings-Locations. To upload a project, try changing the extension to e.g. .curaproject.3mf.zip so that github accepts uploading the file. Otherwise we recommend http://wetransfer.com, but other file hosts like Google Drive or Dropbox work well too.

Thank you for using Cura!
-->

**Application Version**
<!-- The version of the application this issue occurs with -->

**Platform**
<!-- Information about the platform the issue occurs on -->

**Qt**
<!-- The version of Qt used (not necessary if you're using the version from Ultimaker's website) -->

**PyQt**
<!-- The version of PyQt used (not necessary if you're using the version from Ultimaker's website) -->

**Display Driver**
<!--  Video driver name and version -->

**Steps to Reproduce**
<!-- Add the steps needed that lead up to the issue (replace this text) -->

**Actual Results**
<!-- What happens after the above steps have been followed (replace this text) -->

**Expected results**
<!-- What should happen after the above steps have been followed (replace this text) -->

**Additional Information**
<!-- Extra information relevant to the issue, like screenshots (replace this text) -->

