CMake Error at C:/Program Files/CMake/share/cmake-3.23/Modules/CMakeTestCCompiler.cmake:69 (message):
  The C compiler

    "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/bin/Hostarm64/x64/cl.exe"

  is not able to compile a simple test program.

  It fails with the following output:

    Change Dir: C:/Users/<USER>/CuraDev/CuraEngine/build/Debug/CMakeFiles/CMakeTmp
    
    Run Build Command(s):C:/PROGRA~1/MICROS~1/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe cmTC_2b0b8 && [1/2] Building C object CMakeFiles\cmTC_2b0b8.dir\testCCompiler.c.obj
    [2/2] Linking C executable cmTC_2b0b8.exe
    FAILED: cmTC_2b0b8.exe 
    cmd.exe /C "cd . && "C:\Program Files\CMake\bin\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\cmTC_2b0b8.dir --rc=C:\PROGRA~2\WI3CF2~1\10\bin\100226~1.0\arm64\rc.exe --mt=C:\PROGRA~2\WI3CF2~1\10\bin\100226~1.0\arm64\mt.exe --manifests  -- C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1438~1.331\bin\HOSTAR~1\x64\link.exe /nologo CMakeFiles\cmTC_2b0b8.dir\testCCompiler.c.obj  /out:cmTC_2b0b8.exe /implib:cmTC_2b0b8.lib /pdb:cmTC_2b0b8.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
    LINK Pass 1: command "C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1438~1.331\bin\HOSTAR~1\x64\link.exe /nologo CMakeFiles\cmTC_2b0b8.dir\testCCompiler.c.obj /out:cmTC_2b0b8.exe /implib:cmTC_2b0b8.lib /pdb:cmTC_2b0b8.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\cmTC_2b0b8.dir/intermediate.manifest CMakeFiles\cmTC_2b0b8.dir/manifest.res" failed (exit code 1104) with the following output:
    LINK : fatal error LNK1104: 无法打开文件“kernel32.lib”

    ninja: build stopped: subcommand failed.
    
    

  

  CMake will not be able to correctly generate this project.
Call Stack (most recent call first):
  CMakeLists.txt:6 (project)


