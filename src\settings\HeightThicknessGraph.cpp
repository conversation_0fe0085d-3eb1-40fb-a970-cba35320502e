// Copyright (c) 2022 Ultimaker B.V.
// CuraEngine is released under the terms of the AGPLv3 or higher

#include "settings/HeightThicknessGraph.h"

#include <spdlog/spdlog.h>

namespace cura
{

double HeightThicknessGraph::getThickness(const double height, const double layer0_thickness, const double layerX_thickness, const double maxMeshZ) const
{
    if (data_.size() == 0 || height > maxMeshZ)
    {
        return layerX_thickness;
    }
    if (height <= 0)
    {
        return layer0_thickness;
    }
    Datum datum_min(0.0, layer0_thickness);
    Datum datum_max(0.0, layer0_thickness);
    unsigned int datum_idx = 0;
    for (datum_idx; datum_idx < data_.size(); datum_idx++)
    {

        if (height > data_[datum_idx].height_)
        {
            datum_min = data_[datum_idx];
            datum_max = data_[datum_idx];
        }
        else
        {
            datum_max = data_[datum_idx];
            break;
        }
    }
    if (datum_idx == 0)
        datum_min = { 0.0, layer0_thickness };
    double thickness; 
    if (datum_min.height_ == datum_max.height_)
        thickness = datum_min.thickness_;
    else
        thickness = datum_min.thickness_ + (height - datum_min.height_) / (datum_max.height_ - datum_min.height_) * (datum_max.thickness_ - datum_min.thickness_);
    if (height + thickness >= maxMeshZ)
        thickness = maxMeshZ - height;
    return thickness;
}

} // namespace cura
