---
name: unit-test
on:
  push:
    paths:
      - 'include/**'
      - 'src/**'
      - 'tests/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - 'CMakeLists.txt'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/unit-test-post.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - '[0-9]+.[0-9]+'

  pull_request:
    types: [ opened, reopened, synchronize ]
    paths:
      - 'include/**'
      - 'src/**'
      - 'tests/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - 'CMakeLists.txt'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/unit-test-post.yml'
    branches:
      - main
      - '[0-9]+.[0-9]+'

permissions:
  contents: read

env:
  CONAN_LOGIN_USERNAME: ${{ secrets.CONAN_USER }}
  CONAN_PASSWORD: ${{ secrets.CONAN_PASS }}

jobs:
  conan-recipe-version:
    uses: ultimaker/cura-workflows/.github/workflows/conan-recipe-version.yml@main
    with:
      project_name: curaengine

  testing:
    uses: ultimaker/cura-workflows/.github/workflows/unit-test.yml@main
    needs: [ conan-recipe-version ]
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
      conan_extra_args: '-c tools.build:skip_test=False'
      unit_test_cmd: 'ctest --output-junit engine_test.xml'
      unit_test_dir: 'build/Release'
      build: true
    secrets: inherit

